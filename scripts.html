<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="author" content="Lunarbine">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6FootScripts - Scripts</title>
    <link rel="stylesheet" href="src/styles.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>6F</h1>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="scripts.html">Scripts</a></li>
                <li><a href="contact.html">Contact Us</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-toggle-icon">🌙</span>
                </button>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Our Scripts</h1>
                <p>Discover our collection of premium game scripts, carefully crafted and regularly updated for the best gaming experience.</p>
            </div>
        </section>
        <section class="scripts-filters">
            <div class="container">
                <div class="filter-controls">
                    <input type="text" class="search-input" placeholder="Search scripts...">
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">All Scripts</button>
                        <button class="filter-btn" data-filter="active">✅ Active</button>
                        <button class="filter-btn" data-filter="discontinued">🚫 Discontinued</button>
                        <button class="filter-btn" data-filter="maintenance">🔄 Maintenance</button>
                        <button class="filter-btn" data-filter="coming-soon">🔜 Coming Soon</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="scripts-grid-section">
            <div class="container">
                <div class="scripts-grid">
                    <div class="script-card-detailed active" data-category="active" data-game="aopg">
                        <div class="script-image">
                            <div class="game-logo">AOPG</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>A One Piece Game</h3>
                                <span class="status-badge active">✅ Active</span>
                            </div>
                            <p class="script-description">Complete auto farming script with GUI, auto quest, auto stats, and more features.</p>
                            <div class="code-block">
                                <code>loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/AOPG/main/script.lua"))()</code>
                                <button class="copy-btn" data-code='loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/AOPG/main/script.lua"))()'>Copy Script</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed active" data-category="active" data-game="pet-sim-99">
                        <div class="script-image">
                            <div class="game-logo">PS99</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Pet Simulator 99</h3>
                                <span class="status-badge active">✅ Active</span>
                            </div>
                            <p class="script-description">Advanced pet simulator script with auto hatch, auto farm, and trading features.</p>
                            <div class="code-block">
                                <code>loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/PS99/main/script.lua"))()</code>
                                <button class="copy-btn" data-code='loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/PS99/main/script.lua"))()'>Copy Script</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed maintenance" data-category="maintenance" data-game="pets-go">
                        <div class="script-image">
                            <div class="game-logo">PG</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Pets Go</h3>
                                <span class="status-badge maintenance">🔄 Maintenance</span>
                            </div>
                            <p class="script-description">Multi-feature script for Pets Go - Currently under maintenance for new updates.</p>
                            <div class="code-block disabled">
                                <code>Script temporarily unavailable</code>
                                <button class="copy-btn disabled">Under Maintenance</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed discontinued" data-category="discontinued" data-game="old-game">
                        <div class="script-image">
                            <div class="game-logo">OLD</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Old Game Script</h3>
                                <span class="status-badge discontinued">🚫 Discontinued</span>
                            </div>
                            <p class="script-description">This script is no longer maintained due to game updates.</p>
                            <div class="code-block disabled">
                                <code>Script discontinued</code>
                                <button class="copy-btn disabled">No Longer Available</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed coming-soon" data-category="coming-soon" data-game="new-game">
                        <div class="script-image">
                            <div class="game-logo">NEW</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Upcoming Game Script</h3>
                                <span class="status-badge coming-soon">🔜 Coming Soon</span>
                            </div>
                            <p class="script-description">New script in development - Stay tuned for release!</p>
                            <div class="code-block disabled">
                                <code>Coming soon...</code>
                                <button class="copy-btn disabled">In Development</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 6FootScripts. All rights reserved.</p>
        </div>
    </footer>
    <div class="toast" id="toast">Script copied to clipboard!</div>
    <script src="src/script.js"></script>
</body>
</html>
