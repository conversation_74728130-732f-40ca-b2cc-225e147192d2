<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="author" content="Lunarbine">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6FootScripts - Scripts</title>
    <link rel="stylesheet" href="src/styles.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>6F</h1>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="scripts.html">Scripts</a></li>
                <li><a href="contact.html">Contact Us</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-toggle-icon">🌙</span>
                </button>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Our Scripts</h1>
                <p>Discover our collection of premium game scripts, carefully crafted and regularly updated for the best gaming experience.</p>
            </div>
        </section>
        <section class="scripts-filters">
            <div class="container">
                <div class="filter-controls">
                    <input type="text" class="search-input" placeholder="Search scripts...">
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">All Scripts</button>
                        <button class="filter-btn" data-filter="active">✅ Active</button>
                        <button class="filter-btn" data-filter="discontinued">🚫 Discontinued</button>
                        <button class="filter-btn" data-filter="maintenance">🔄 Maintenance</button>
                        <button class="filter-btn" data-filter="coming-soon">🔜 Coming Soon</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="scripts-grid-section">
            <div class="container">
                <div class="scripts-grid">
                    <div class="script-card-detailed active" data-category="active" data-game="aopg">
                        <div class="script-image">
                            <div class="game-logo">AOPG</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>A One Piece Game</h3>
                                <span class="status-badge active">✅ Active</span>
                            </div>
                            <p class="script-description">Complete auto farming script with GUI, auto quest, auto stats, and more features.</p>
                            <div class="code-block">
                                <code>loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/AOPG/main/script.lua"))()</code>
                                <button class="copy-btn" data-code='loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/AOPG/main/script.lua"))()'>Copy Script</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed active" data-category="active" data-game="pet-sim-99">
                        <div class="script-image">
                            <div class="game-logo">PS99</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Pet Simulator 99</h3>
                                <span class="status-badge active">✅ Active</span>
                            </div>
                            <p class="script-description">Advanced pet simulator script with auto hatch, auto farm, and trading features.</p>
                            <div class="code-block">
                                <code>loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/PS99/main/script.lua"))()</code>
                                <button class="copy-btn" data-code='loadstring(game:HttpGet("https://raw.githubusercontent.com/6FootScripts/PS99/main/script.lua"))()'>Copy Script</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed maintenance" data-category="maintenance" data-game="pets-go">
                        <div class="script-image">
                            <div class="game-logo">PG</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Pets Go</h3>
                                <span class="status-badge maintenance">🔄 Maintenance</span>
                            </div>
                            <p class="script-description">Multi-feature script for Pets Go - Currently under maintenance for new updates.</p>
                            <div class="code-block disabled">
                                <code>Script temporarily unavailable</code>
                                <button class="copy-btn disabled">Under Maintenance</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed discontinued" data-category="discontinued" data-game="old-game">
                        <div class="script-image">
                            <div class="game-logo">OLD</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Old Game Script</h3>
                                <span class="status-badge discontinued">🚫 Discontinued</span>
                            </div>
                            <p class="script-description">This script is no longer maintained due to game updates.</p>
                            <div class="code-block disabled">
                                <code>Script discontinued</code>
                                <button class="copy-btn disabled">No Longer Available</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed coming-soon" data-category="coming-soon" data-game="new-game">
                        <div class="script-image">
                            <div class="game-logo">NEW</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Upcoming Game Script</h3>
                                <span class="status-badge coming-soon">🔜 Coming Soon</span>
                            </div>
                            <p class="script-description">New script in development - Stay tuned for release!</p>
                            <div class="code-block disabled">
                                <code>Coming soon...</code>
                                <button class="copy-btn disabled">In Development</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed maintenance" data-category="maintenance" data-game="blox-fruits">
                        <div class="script-image">
                            <div class="game-logo">🦅 🔫  aopg better</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Blox Fruits</h3>
                                <span class="status-badge maintenance">🔄 Maintenance</span>
                            </div>
                            <p class="script-description"> i wanna see tako work on blox fruit pls now</p>
                            <div class="code-block disabled">
                                <code>Script down due tako lazyness</code>
                                <button class="copy-btn disabled">Under Maintenance</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed discontinued" data-category="discontinued" data-game="arsenal">
                        <div class="script-image">
                            <div class="game-logo">😎 💀</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>Arsenal</h3>
                                <span class="status-badge discontinued">🚫 Discontinued</span>
                            </div>
                            <p class="script-description">Arsenal script pls</p>
                            <div class="code-block disabled">
                                <code>Script discontinued - you know it tako got me banned cause he lazy and didnt patch that anti-cheat</code>
                                <button class="copy-btn disabled">No Longer Available</button>
                            </div>
                        </div>
                    </div>
                    <div class="script-card-detailed coming-soon" data-category="coming-soon" data-game="doors">
                        <div class="script-image">
                            <div class="game-logo">🤣</div>
                        </div>
                        <div class="script-info">
                            <div class="script-header">
                                <h3>only game i know are those</h3>
                                <span class="status-badge coming-soon">🔜 Coming Soon</span>
                            </div>
                            <p class="script-description">yeah idk what to yap here</p>
                            <div class="code-block disabled">
                                <code>Beta testing in progress...</code>
                                <button class="copy-btn disabled">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="request-script-section">
            <div class="container">
                <h2>Request a Script</h2>
                <p>Can't find a script for your favorite game? Request it and we'll consider adding it to our collection!</p>
                <div class="request-form-container">
                    <form class="request-form" id="request-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="requester-name">Your Name</label>
                                <input type="text" id="requester-name" name="requesterName" required minlength="2" maxlength="50">
                            </div>
                            <div class="form-group">
                                <label for="requester-discord">Discord Username (Optional)</label>
                                <input type="text" id="requester-discord" name="requesterDiscord" placeholder="username#1234" maxlength="50">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="game-name">Game Name</label>
                            <input type="text" id="game-name" name="gameName" required minlength="2" maxlength="100" placeholder="Enter the exact game name">
                        </div>
                        <div class="form-group">
                            <label for="game-link">Game Link</label>
                            <input type="url" id="game-link" name="gameLink" required placeholder="https://www.roblox.com/games/..." pattern="https://.*">
                        </div>
                        <div class="form-group">
                            <label for="request-priority">Priority Level</label>
                            <select id="request-priority" name="priority" required>
                                <option value="">Select priority</option>
                                <option value="low">Low - Just a suggestion</option>
                                <option value="medium">Medium - Would be nice to have</option>
                                <option value="high">High - Really need this script</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="features-wanted">Desired Features</label>
                            <textarea id="features-wanted" name="featuresWanted" rows="4" required minlength="10" maxlength="500" placeholder="Describe what features you'd like the script to have (auto-farm, GUI, specific functions, etc.)"></textarea>
                            <small class="char-counter">0/500 characters</small>
                        </div>
                        <div class="form-group">
                            <label for="additional-info">Additional Information</label>
                            <textarea id="additional-info" name="additionalInfo" rows="3" maxlength="300" placeholder="Any other details that might help us (game updates, special requirements, etc.)"></textarea>
                            <small class="char-counter">0/300 characters</small>
                        </div>

                        <!-- Anti-spam measures -->
                        <div class="form-group anti-spam">
                            <label for="verification">Verification: What is 5 + 3?</label>
                            <input type="number" id="verification" name="verification" required min="8" max="8" placeholder="Enter the answer">
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="terms-agree" name="termsAgree" required>
                                <span class="checkmark"></span>
                                I understand that script requests are not guaranteed and development time varies
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="no-spam" name="noSpam" required>
                                <span class="checkmark"></span>
                                I agree not to spam requests and understand that duplicate requests may be ignored
                            </label>
                        </div>

                        <button type="submit" class="cta-button request-submit-btn">
                            <span class="btn-text">Submit Request</span>
                            <span class="btn-loading" style="display: none;">Sending...</span>
                        </button>
                    </form>

                    <div class="request-info">
                        <h3>Request Guidelines</h3>
                        <div class="guideline-item">
                            <span class="guideline-icon">⚡</span>
                            <div>
                                <strong>Response Time</strong>
                                <p>We review requests within 24-48 hours</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🎯</span>
                            <div>
                                <strong>Popular Games Priority</strong>
                                <p>Scripts for trending games are prioritized</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🔒</span>
                            <div>
                                <strong>No Guarantees</strong>
                                <p>Not all requests can be fulfilled due to technical limitations</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🚫</span>
                            <div>
                                <strong>No Spam Policy</strong>
                                <p>Duplicate or spam requests will be automatically filtered</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 6FootScripts. All rights reserved.</p>
        </div>
    </footer>
    <button class="scroll-to-top" id="scroll-to-top" aria-label="Scroll to top">
        <span>↑</span>
    </button>
    <script src="src/script.js"></script>
</body>
</html>
