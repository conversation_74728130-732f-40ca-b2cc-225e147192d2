:root {
    --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --secondary-gradient: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
    --accent-gradient: linear-gradient(135deg, #5f27cd 0%, #341f97 100%);
    --orange-teal-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #00d2d3 100%);
    --purple-blue-gradient: linear-gradient(135deg, #5f27cd 0%, #341f97 50%, #54a0ff 100%);
    --dark-gradient: linear-gradient(135deg, #2c2c54 0%, #1a1a2e 100%);
    --glass-gradient: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 36, 0.1) 100%);
    --text-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --glow-color: rgba(255, 107, 107, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Robot<PERSON>', 'Arial', sans-serif;
    background: linear-gradient(-45deg, #ff6b6b, #ee5a24, #00d2d3, #54a0ff);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    color: white;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.navbar {
    background: rgba(255, 107, 107, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.logo h1 {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 0 0 20px var(--glow-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-menu a:hover {
    background: var(--glass-gradient);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-gradient);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

main {
    margin-top: 80px;
}

.hero {
    min-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 107, 107, 0.3) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
    background: var(--orange-teal-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--glow-color);
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.5)); }
    to { filter: drop-shadow(0 0 40px rgba(255, 107, 107, 0.8)); }
}

.hero p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: var(--primary-gradient);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    color: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 50px rgba(255, 107, 107, 0.7);
}

.featured-scripts, .statistics, .testimonials {
    padding: 6rem 0;
    position: relative;
}

.featured-scripts::before, .statistics::before, .testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 210, 211, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.featured-scripts h2, .statistics h2, .testimonials h2 {
    text-align: center;
    font-size: 3rem;
    margin-bottom: 4rem;
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -1px;
    position: relative;
}

.featured-scripts h2::after, .statistics h2::after, .testimonials h2::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.script-grid, .stats-grid, .testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.script-card:nth-child(1) { animation: float 6s ease-in-out infinite; }
.script-card:nth-child(2) { animation: floatReverse 7s ease-in-out infinite; }
.script-card:nth-child(3) { animation: float 8s ease-in-out infinite; }

.stat-card:nth-child(1) { animation: float 5s ease-in-out infinite; }
.stat-card:nth-child(2) { animation: floatReverse 6s ease-in-out infinite; }
.stat-card:nth-child(3) { animation: float 7s ease-in-out infinite; }
.stat-card:nth-child(4) { animation: floatReverse 8s ease-in-out infinite; }

.script-card, .stat-card, .testimonial-card {
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.script-card::before,
.stat-card::before,
.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--purple-blue-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.script-card:hover::before,
.stat-card:hover::before,
.testimonial-card:hover::before {
    opacity: 0.1;
}

.script-card:hover,
.stat-card:hover,
.testimonial-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
    border-color: rgba(255, 107, 107, 0.3);
}

.script-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.script-header h3 {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
}

.status-badge {
    padding: 0.4rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.status-badge.active {
    background: linear-gradient(135deg, #00d2d3, #0abde3);
}

.status-badge.maintenance {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
}

.status-badge.discontinued {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.status-badge.coming-soon {
    background: linear-gradient(135deg, #5f27cd, #341f97);
}

.script-description {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.script-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.last-updated {
    font-size: 0.9rem;
    opacity: 0.7;
}

.recently-updated {
    background: var(--accent-gradient);
    padding: 0.2rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    width: fit-content;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes floatReverse {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(10px); }
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    background: var(--orange-teal-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.2rem;
    opacity: 0.9;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.7;
    position: relative;
}

.testimonial-card p::before {
    content: '"';
    font-size: 4rem;
    position: absolute;
    top: -1rem;
    left: -1rem;
    color: rgba(255, 107, 107, 0.3);
    font-family: serif;
}

.testimonial-author {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    font-size: 1rem;
}

.scripts-hero, .contact-hero {
    padding: 5rem 0 3rem;
    text-align: center;
}

.scripts-hero h1, .contact-hero h1 {
    font-size: 4rem;
    background: var(--orange-teal-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.scripts-hero p, .contact-hero p {
    font-size: 1.3rem;
    opacity: 0.9;
}

.scripts-filters {
    padding: 2rem 0;
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.8rem 1.5rem;
    color: white;
    font-size: 1rem;
    width: 100%;
    max-width: 400px;
    backdrop-filter: blur(10px);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    outline: none;
    border-color: rgba(255, 107, 107, 0.5);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.scripts-grid-section {
    padding: 3rem 0;
}

.scripts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.script-card-detailed {
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.script-card-detailed.discontinued {
    opacity: 0.6;
    filter: grayscale(50%);
}

.script-card-detailed.maintenance {
    border-color: rgba(254, 202, 87, 0.3);
}

.script-card-detailed:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.3);
}

.script-image {
    height: 150px;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.script-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.game-logo {
    font-size: 3rem;
    font-weight: bold;
    color: white;
    text-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.script-info {
    padding: 2rem;
}

.code-block {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-block code {
    color: #00d2d3;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    word-break: break-all;
    display: block;
    margin-bottom: 1rem;
}

.copy-btn {
    background: var(--accent-gradient);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(95, 39, 205, 0.4);
}

.copy-btn.disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    opacity: 0.6;
}

.copy-btn.disabled:hover {
    transform: none;
    box-shadow: none;
}

.code-block.disabled {
    opacity: 0.5;
}

.team-section {
    padding: 3rem 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
}

.team-card {
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.3);
}

.team-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.team-info h3 {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.team-role {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.team-description {
    margin-bottom: 1.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.team-responsibilities h4 {
    margin-bottom: 0.8rem;
    color: white;
}

.team-responsibilities ul {
    list-style: none;
    margin-bottom: 2rem;
}

.team-responsibilities li {
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1.5rem;
    opacity: 0.9;
}

.team-responsibilities li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #ff6b6b;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
}

.contact-btn {
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
}

.contact-btn.discord {
    background: linear-gradient(135deg, #5865f2, #4752c4);
}

.contact-btn.server {
    background: var(--secondary-gradient);
}

.contact-btn.request {
    background: var(--accent-gradient);
}

.discord-icon {
    font-size: 1.2rem;
}

.general-contact {
    padding: 3rem 0;
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.general-contact h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-info-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(255, 107, 107, 0.2);
}

.contact-info-card h3 {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.contact-info-card p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.support-hours {
    background: rgba(255, 107, 107, 0.1);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.faq-section {
    padding: 3rem 0;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.faq-grid {
    display: grid;
    gap: 1.5rem;
}

.faq-item {
    background: var(--glass-gradient);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.2);
}

.faq-question {
    background: rgba(255, 107, 107, 0.1);
    padding: 1.5rem;
    font-weight: bold;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-question:hover {
    background: rgba(255, 107, 107, 0.2);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
    opacity: 1;
}

.footer {
    background: var(--dark-gradient);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 3rem;
}

.footer p {
    opacity: 0.8;
}

.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-gradient);
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(0);
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(255, 107, 107, 0.95);
        backdrop-filter: blur(20px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .scripts-hero h1,
    .contact-hero h1 {
        font-size: 2.5rem;
    }

    .filter-controls {
        padding: 0 1rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .scripts-grid {
        grid-template-columns: 1fr;
    }

    .script-card-detailed {
        margin: 0 1rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .team-card {
        flex-direction: column;
        text-align: center;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .script-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .scripts-hero h1,
    .contact-hero h1 {
        font-size: 2rem;
    }

    .featured-scripts h2,
    .statistics h2,
    .testimonials h2 {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .team-card {
        padding: 1.5rem;
    }

    .avatar-placeholder {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .team-info h3 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .search-input {
        font-size: 0.9rem;
    }

    .code-block code {
        font-size: 0.8rem;
    }
}

.hidden {
    display: none !important;
}

.script-card-detailed.hidden {
    display: none;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.script-card-detailed {
    animation: fadeIn 0.5s ease forwards;
}

.loading {
    opacity: 0.5;
    pointer-events: none;
}

.copy-btn.copying {
    background: linear-gradient(135deg, #00d2d3, #0abde3);
    transform: scale(0.95);
}

.copy-btn.copying::after {
    content: ' ✓';
}

.nav-menu a.active {
    background: var(--primary-gradient);
    color: white;
}

.filter-btn:active {
    transform: translateY(0);
}

.script-card-detailed:nth-child(even) {
    animation-delay: 0.1s;
}

.script-card-detailed:nth-child(odd) {
    animation-delay: 0.2s;
}

.team-card:hover .avatar-placeholder {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.faq-question:active {
    transform: scale(0.98);
}

.contact-btn:active {
    transform: translateY(0);
}

.cta-button:active {
    transform: translateY(-1px);
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}