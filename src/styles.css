 /* Light theme colors make changes here if needed */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-card: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark theme colors make changes here if needed */

[data-theme="dark"] {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-card: #1f2937;
    --border-color: #374151;
    --border-light: #4b5563;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    font-size: 16px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.logo h1 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-menu a:hover {
    color: var(--primary-color);
    background: var(--bg-secondary);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    background: var(--border-light);
    transform: scale(1.05);
}

.theme-toggle-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-toggle-icon {
    transform: rotate(20deg);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

main {
    margin-top: 80px;
}

.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 0 2rem;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    line-height: 1.1;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background: var(--primary-color);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.request-btn {
    background: var(--secondary-color) !important;
}

.request-btn:hover {
    background: #059669 !important;
}

.featured-scripts, .statistics, .testimonials {
    padding: 5rem 0;
}

.statistics {
    background: var(--bg-secondary);
}

.featured-scripts h2, .statistics h2, .testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.2;
}

.featured-scripts p, .statistics p, .testimonials p {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 3rem;
}

.script-grid, .stats-grid, .testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.script-card, .stat-card, .testimonial-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.script-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.script-card:hover::before {
    opacity: 1;
}

.script-card:hover,
.stat-card:hover,
.testimonial-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.script-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.script-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.script-header h3::before {
    content: '🎮';
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    border: none;
}

.status-badge.active {
    background: var(--secondary-color);
}

.status-badge.maintenance {
    background: var(--accent-color);
}

.status-badge.discontinued {
    background: #ef4444;
}

.status-badge.coming-soon {
    background: var(--primary-color);
}

.script-description {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}

.script-meta {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.last-updated {
    font-size: 0.875rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.last-updated::before {
    content: '🕒';
    font-size: 0.875rem;
}

.recently-updated {
    background: var(--secondary-color);
    color: white;
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    width: fit-content;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.recently-updated::before {
    content: '✨';
    font-size: 0.75rem;
}

.stat-card {
    text-align: center;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.stat-card:hover::before {
    opacity: 0.1;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    line-height: 1;
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.stat-label::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.testimonial-card {
    position: relative;
    padding: 2.5rem 2rem 2rem;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    left: 2rem;
    font-size: 3rem;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: serif;
    line-height: 1;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 2rem;
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-secondary);
    position: relative;
    z-index: 1;
}

.testimonial-author {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.testimonial-author::before {
    content: '👤';
    font-size: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-info {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    height: fit-content;
}

.contact-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-method:last-child {
    margin-bottom: 0;
}

.contact-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-method strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-method p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    text-align: left;
}

/* Team Section Styles */
.team-section {
    padding: 5rem 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.team-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2.5rem;
    transition: all 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
    box-shadow: var(--shadow-sm);
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.team-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.team-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    box-shadow: var(--shadow-md);
}

.team-info h3 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.team-role {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.team-description {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.team-responsibilities h4 {
    margin-bottom: 0.8rem;
    color: var(--text-primary);
    font-weight: 600;
}

.team-responsibilities ul {
    list-style: none;
    margin-bottom: 2rem;
}

.team-responsibilities li {
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: var(--text-secondary);
}

.team-responsibilities li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.contact-buttons {
    display: flex;
    gap: 1rem;
}

.contact-btn {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
}

.contact-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.contact-btn.discord {
    background: #5865f2;
}

.contact-btn.discord:hover {
    background: #4752c4;
}

.contact-btn.server {
    background: var(--secondary-color);
}

.contact-btn.server:hover {
    background: #059669;
}

.contact-btn.request {
    background: var(--accent-color);
}

.contact-btn.request:hover {
    background: #d97706;
}

/* General Contact and FAQ Styles */
.general-contact {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.general-contact h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.contact-info-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.contact-info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.contact-info-card h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-info-card p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.support-hours {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.faq-section {
    padding: 5rem 0;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.faq-grid {
    display: grid;
    gap: 1.5rem;
    margin-top: 3rem;
}

.faq-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.faq-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.faq-question {
    background: var(--bg-secondary);
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: var(--text-primary);
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    transition: transform 0.3s ease;
    color: var(--primary-color);
}

.faq-item.active .faq-question::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-question:hover {
    background: var(--border-light);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    color: var(--text-secondary);
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
    opacity: 1;
}

/* Scripts Page Styles */
.scripts-filters {
    padding: 3rem 0;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.filter-controls {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.search-input {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    width: 100%;
    max-width: 400px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.search-input::placeholder {
    color: var(--text-light);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.scripts-grid-section {
    padding: 5rem 0;
}

.scripts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.script-card-detailed {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.script-card-detailed:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.script-card-detailed.discontinued {
    opacity: 0.7;
    filter: grayscale(30%);
}

.script-card-detailed.maintenance {
    border-color: var(--accent-color);
}

.script-card-detailed.coming-soon {
    border-color: var(--secondary-color);
}

.script-image {
    height: 150px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.script-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.script-card-detailed:hover .script-image::before {
    transform: translateX(100%);
}

.game-logo {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1;
    position: relative;
}

.script-info {
    padding: 2rem;
}

.script-info .script-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.script-info .script-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    flex: 1;
}

.script-info .script-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.code-block {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    position: relative;
}

.code-block code {
    color: var(--primary-color);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    word-break: break-all;
    display: block;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.copy-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
    width: 100%;
}

.copy-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.copy-btn.disabled {
    background: var(--text-light);
    cursor: not-allowed;
    opacity: 0.6;
}

.copy-btn.disabled:hover {
    transform: none;
    box-shadow: none;
    background: var(--text-light);
}

.code-block.disabled {
    opacity: 0.6;
    background: var(--border-light);
}

.code-block.disabled code {
    color: var(--text-light);
}

.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
    font-weight: 600;
}

.toast.show {
    transform: translateX(0);
}

/* Script Request Form Styles */
.request-script-section {
    padding: 5rem 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.request-script-section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.request-script-section p {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 3rem;
}

.request-form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.request-form {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.char-counter {
    display: block;
    text-align: right;
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

.anti-spam {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.anti-spam label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    accent-color: var(--primary-color);
}

.request-submit-btn {
    width: 100%;
    position: relative;
    overflow: hidden;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.request-info {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    height: fit-content;
}

.request-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.guideline-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.guideline-item:last-child {
    margin-bottom: 0;
}

.guideline-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.guideline-item strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.guideline-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    text-align: left;
}

/* Form validation styles */
.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown),
.form-group textarea:invalid:not(:placeholder-shown) {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: var(--secondary-color);
}

/* Rate limiting indicator */
.rate-limit-warning {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    color: #92400e;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

[data-theme="dark"] .rate-limit-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.script-card, .stat-card, .testimonial-card, .team-card, .script-card-detailed {
    animation: fadeInUp 0.6s ease forwards;
}

.script-card:nth-child(1), .script-card-detailed:nth-child(1) { animation-delay: 0.1s; }
.script-card:nth-child(2), .script-card-detailed:nth-child(2) { animation-delay: 0.2s; }
.script-card:nth-child(3), .script-card-detailed:nth-child(3) { animation-delay: 0.3s; }
.script-card:nth-child(4), .script-card-detailed:nth-child(4) { animation-delay: 0.4s; }
.script-card:nth-child(5), .script-card-detailed:nth-child(5) { animation-delay: 0.5s; }

.theme-toggle:focus,
.cta-button:focus,
.contact-btn:focus,
.copy-btn:focus,
.filter-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

html {
    scroll-behavior: smooth;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.copy-btn.copying {
    background: var(--secondary-color) !important;
    transform: scale(0.95);
}

.copy-btn.copying::after {
    content: ' ✓';
}

.nav-menu a,
.theme-toggle,
.filter-btn,
.contact-btn,
.copy-btn {
    position: relative;
    overflow: hidden;
}

.nav-menu a::before,
.theme-toggle::before,
.filter-btn::before,
.contact-btn::before,
.copy-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.nav-menu a:hover::before,
.theme-toggle:hover::before,
.filter-btn:hover::before,
.contact-btn:hover::before,
.copy-btn:hover::before {
    width: 100%;
    height: 100%;
}

.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.status-badge:hover::before {
    left: 100%;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: var(--secondary-color);
}

.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown),
.form-group textarea:invalid:not(:placeholder-shown) {
    border-color: #ef4444;
}

.script-card::after,
.stat-card::after,
.testimonial-card::after,
.team-card::after,
.script-card-detailed::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    border-radius: inherit;
}

.script-card:hover::after,
.stat-card:hover::after,
.testimonial-card:hover::after,
.team-card:hover::after,
.script-card-detailed:hover::after {
    opacity: 0.03;
}

.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Floating Request Button */
.floating-request-btn {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px);
}

.floating-request-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.floating-request-btn:hover {
    background: #059669;
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.request-icon {
    font-size: 1.2rem;
}

.request-text {
    white-space: nowrap;
}

::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
}

button:focus-visible,
.cta-button:focus-visible,
.contact-btn:focus-visible,
.copy-btn:focus-visible,
.filter-btn:focus-visible,
.theme-toggle:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.form-group input:user-invalid,
.form-group select:user-invalid,
.form-group textarea:user-invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input:user-valid,
.form-group select:user-valid,
.form-group textarea:user-valid {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.toast.error {
    background: #ef4444;
}

.toast.warning {
    background: var(--accent-color);
}

.toast.info {
    background: #3b82f6;
}

.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.script-card,
.stat-card,
.testimonial-card,
.team-card,
.script-card-detailed {
    will-change: transform;
}

.script-card:hover,
.stat-card:hover,
.testimonial-card:hover,
.team-card:hover,
.script-card-detailed:hover {
    will-change: auto;
}

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .script-card,
    .stat-card,
    .testimonial-card,
    .team-card,
    .script-card-detailed {
        animation: none !important;
    }
}

@media print {
    .navbar,
    .scroll-to-top,
    .theme-toggle,
    .hamburger,
    .toast,
    .page-loader {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .script-card,
    .stat-card,
    .testimonial-card,
    .team-card,
    .script-card-detailed {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }

    .script-card,
    .stat-card,
    .testimonial-card,
    .team-card,
    .script-card-detailed {
        border-width: 2px;
    }
}

.footer {
    background: var(--bg-secondary);
    padding: 3rem 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.footer p {
    color: var(--text-secondary);
    margin: 0;
}


@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-color);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .featured-scripts h2,
    .statistics h2,
    .testimonials h2 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .script-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .team-card {
        flex-direction: column;
        text-align: center;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-controls {
        padding: 0 1rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .scripts-grid {
        grid-template-columns: 1fr;
    }

    .script-card-detailed {
        margin: 0 1rem;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .featured-scripts h2,
    .statistics h2,
    .testimonials h2 {
        font-size: 1.75rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .contact-info {
        padding: 1.5rem;
    }

    .team-card {
        padding: 1.5rem;
    }

    .avatar-placeholder {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .search-input {
        font-size: 0.9rem;
    }

    .code-block code {
        font-size: 0.8rem;
    }

    .script-info {
        padding: 1.5rem;
    }

    .request-form-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .request-form,
    .request-info {
        padding: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .floating-request-btn {
        bottom: 1rem;
        left: 1rem;
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    .request-text {
        display: none;
    }

    .floating-request-btn .request-icon {
        font-size: 1.5rem;
    }
}

.nav-menu a.active {
    background: var(--primary-color);
    color: white;
}

.cta-button:active {
    transform: translateY(-1px);
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}