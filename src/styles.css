:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-card: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    font-size: 16px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.logo h1 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-menu a:hover {
    color: var(--primary-color);
    background: var(--bg-secondary);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

main {
    margin-top: 80px;
}

.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 0 2rem;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    line-height: 1.1;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background: var(--primary-color);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.featured-scripts, .statistics, .testimonials {
    padding: 5rem 0;
}

.statistics {
    background: var(--bg-secondary);
}

.featured-scripts h2, .statistics h2, .testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.2;
}

.featured-scripts p, .statistics p, .testimonials p {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 3rem;
}

.script-grid, .stats-grid, .testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.script-card, .stat-card, .testimonial-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.script-card:hover,
.stat-card:hover,
.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.script-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.script-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    border: none;
}

.status-badge.active {
    background: var(--secondary-color);
}

.status-badge.maintenance {
    background: var(--accent-color);
}

.status-badge.discontinued {
    background: #ef4444;
}

.status-badge.coming-soon {
    background: var(--primary-color);
}

.script-description {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.script-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.last-updated {
    font-size: 0.875rem;
    color: var(--text-light);
}

.recently-updated {
    background: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    width: fit-content;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.testimonial-author {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

/* Clean up - removing unused complex styles */

/* Removed team and contact page styles - not used on main page */

.footer {
    background: var(--bg-secondary);
    padding: 3rem 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
}

.footer p {
    color: var(--text-secondary);
    margin: 0;
}

/* Removed toast notification styles */

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-color);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .scripts-hero h1,
    .contact-hero h1 {
        font-size: 2.5rem;
    }

    .filter-controls {
        padding: 0 1rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .scripts-grid {
        grid-template-columns: 1fr;
    }

    .script-card-detailed {
        margin: 0 1rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .team-card {
        flex-direction: column;
        text-align: center;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .script-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .scripts-hero h1,
    .contact-hero h1 {
        font-size: 2rem;
    }

    .featured-scripts h2,
    .statistics h2,
    .testimonials h2 {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .team-card {
        padding: 1.5rem;
    }

    .avatar-placeholder {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .team-info h3 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .search-input {
        font-size: 0.9rem;
    }

    .code-block code {
        font-size: 0.8rem;
    }
}

.hidden {
    display: none !important;
}

.script-card-detailed.hidden {
    display: none;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.script-card-detailed {
    animation: fadeIn 0.5s ease forwards;
}

.loading {
    opacity: 0.5;
    pointer-events: none;
}

.copy-btn.copying {
    background: linear-gradient(135deg, #00d2d3, #0abde3);
    transform: scale(0.95);
}

.copy-btn.copying::after {
    content: ' ✓';
}

.nav-menu a.active {
    background: var(--primary-gradient);
    color: white;
}

.filter-btn:active {
    transform: translateY(0);
}

.script-card-detailed:nth-child(even) {
    animation-delay: 0.1s;
}

.script-card-detailed:nth-child(odd) {
    animation-delay: 0.2s;
}

.team-card:hover .avatar-placeholder {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.faq-question:active {
    transform: scale(0.98);
}

.contact-btn:active {
    transform: translateY(0);
}

.cta-button:active {
    transform: translateY(-1px);
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}